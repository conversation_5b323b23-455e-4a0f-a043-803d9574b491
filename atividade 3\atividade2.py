def eh_primo(n):
    if n < 2:
        return False
    for i in range(2, int(n**0.5) + 1):
        if n % i == 0:
            return False
    return True

entrada = input("Digite o intervalo (minimo maximo): ")
numeros = list(map(int, entrada.split()))
minimo, maximo = numeros[0], numeros[1]

pares = 0
primos = 0

for numero in range(minimo, maximo + 1):
    if numero % 2 == 0:
        pares += 1
    if eh_primo(numero):
        primos += 1

print(f"Quantidade de números pares: {pares}")
print(f"Quantidade de números primos: {primos}")